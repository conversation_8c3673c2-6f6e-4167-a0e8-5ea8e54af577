# Sets default memory used for gradle commands. Can be overridden by user or command line properties.
# This is required to provide enough memory for the Minecraft decompilation process.
org.gradle.jvmargs=-Xmx4G
org.gradle.daemon=false
org.gradle.configuration-cache=true

mod_version = 2.2
enable_extras=true

minecraft_version = 1.20.1
forge_version = 47.4.3
parchment_version = 2023.09.03
mixinextras_version = 0.4.1
mixin_version = 0.8.5

#mod dependency versions
create_version = 6.0.6-150
ponder_version = 1.0.80
flywheel_version = 1.0.4
registrate_version = MC1.20-1.3.3
#QOL for when I'm testing
jei_version = 15.20.0.106
tmrv_version = 0.6.0
emi_version = 1.1.20
modernfix_version = 5.24.0
embeddium_version = 0.3.31
# (Create+ modpack) mods that have integration
berry_good_version = 7.0.0
blueprint_version = 7.1.2

